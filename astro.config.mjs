import tailwind from "@astrojs/tailwind";
import { defineConfig } from "astro/config";
import mdx from "@astrojs/mdx";
import sitemap from "@astrojs/sitemap";
import pagefind from "astro-pagefind";
import path from 'path';

// https://astro.build/config
export default defineConfig({
  site: "https://valon.github.io",
  base: "/",
  vite: {
    resolve: {
      alias: {
        '@components': path.resolve('./src/components'),
        '@layouts': path.resolve('./src/layouts'),
        '@lib': path.resolve('./src/lib'),
        '@consts': path.resolve('./src/consts.ts')
      }
    }
  },
  integrations: [
    tailwind(),
    sitemap(),
    mdx(),
    pagefind({
      indexing: {
        verbose: true,
        excludeSelectors: ["nav", "footer", ".toc"],
      },
    }),
  ],
  markdown: {
    shikiConfig: {
      theme: "css-variables",
    },
  },
});
