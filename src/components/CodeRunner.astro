---
import Throbbers from "./Throbbers.astro";

interface Props {
  code: string;
  language?: string;
  title?: string;
  output?: string;
}

const { code, language = "python", title = "Try it yourself", output = "" } = Astro.props;
const runnerId = `runner-${Math.random().toString(36).substr(2, 9)}`;
---

<div class="code-runner border border-black/15 dark:border-white/20 rounded-lg p-4 my-6 bg-neutral-50 dark:bg-neutral-800/50">
  <div class="flex justify-between items-center mb-4">
    <h3 class="font-semibold text-black dark:text-white">{title}</h3>
    <button 
      id={`run-${runnerId}`}
      class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200 flex items-center gap-2"
    >
      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
      </svg>
      <span id={`run-text-${runnerId}`}>Run Code</span>
    </button>
  </div>
  
  <pre class="bg-neutral-100 dark:bg-neutral-900 p-3 rounded mb-4 overflow-x-auto"><code class={`language-${language}`}>{code}</code></pre>
  
  <div id={`output-${runnerId}`} class="hidden">
    <!-- Loading State -->
    <div id={`loading-${runnerId}`} class="flex items-center justify-center p-4 bg-black rounded">
      <div class="text-center">
        <Throbbers type="wave" size="sm" color="#10B981" />
        <p class="mt-2 text-xs text-green-400">Running code...</p>
      </div>
    </div>
    
    <!-- Output Result -->
    <div id={`result-${runnerId}`} class="hidden bg-black text-green-400 p-3 rounded font-mono text-sm">
      <div class="flex items-center gap-2 mb-2 text-green-300">
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
        </svg>
        <span>Output:</span>
      </div>
      <pre id={`output-text-${runnerId}`}></pre>
    </div>
  </div>
</div>

<script define:vars={{ runnerId, code, output }}>
  document.addEventListener('DOMContentLoaded', () => {
    const runButton = document.getElementById(`run-${runnerId}`);
    const runText = document.getElementById(`run-text-${runnerId}`);
    const outputContainer = document.getElementById(`output-${runnerId}`);
    const loading = document.getElementById(`loading-${runnerId}`);
    const result = document.getElementById(`result-${runnerId}`);
    const outputText = document.getElementById(`output-text-${runnerId}`);
    
    if (runButton) {
      runButton.addEventListener('click', async () => {
        // Disable button
        runButton.disabled = true;
        runButton.classList.add('opacity-50', 'cursor-not-allowed');
        runText.textContent = 'Running...';
        
        // Show output container and loading
        outputContainer?.classList.remove('hidden');
        loading?.classList.remove('hidden');
        result?.classList.add('hidden');
        
        try {
          // Simulate code execution with different delays for realism
          const executionTime = Math.random() * 2000 + 1000; // 1-3 seconds
          await new Promise(resolve => setTimeout(resolve, executionTime));
          
          // Hide loading and show result
          loading?.classList.add('hidden');
          result?.classList.remove('hidden');
          
          // Set output text
          if (outputText) {
            outputText.textContent = output || 'Code executed successfully!';
          }
          
          // Update button text
          runText.textContent = 'Run Again';
          
        } catch (error) {
          // Handle errors
          loading?.classList.add('hidden');
          result?.classList.remove('hidden');
          
          if (outputText) {
            outputText.textContent = 'Error: Something went wrong during execution.';
            outputText.parentElement.classList.add('text-red-400');
          }
          
          runText.textContent = 'Try Again';
        } finally {
          // Re-enable button
          runButton.disabled = false;
          runButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
      });
    }
  });
</script>
