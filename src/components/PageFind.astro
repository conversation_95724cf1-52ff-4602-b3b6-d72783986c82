---
import Search from "astro-pagefind/components/Search";
import Throbbers from "./Throbbers.astro";
---

<aside data-pagefind-ignore>
  <div
    transition:persist
    id="backdrop"
    class="bg-[rgba(0, 0, 0, 0.5] invisible fixed left-0 top-0 z-50 flex h-screen w-full justify-center p-6 backdrop-blur-sm"
  >
    <div
      id="pagefind-container"
      class="m-0 flex h-fit max-h-[80%] w-full max-w-screen-sm flex-col overflow-auto rounded border border-black/15 bg-neutral-100 p-2 px-4 py-3 shadow-lg dark:border-white/20 dark:bg-neutral-900"
    >
      <!-- Search Loading State -->
      <div
        id="search-loading"
        class="flex hidden items-center justify-center p-6"
      >
        <div class="text-center">
          <Throbbers type="dots" size="md" color="#89B4FA" />
          <p class="mt-2 text-sm text-neutral-600 dark:text-neutral-400">
            Searching...
          </p>
        </div>
      </div>

      <Search
        id="search"
        className="pagefind-ui"
        uiOptions={{
          showImages: false,
          excerptLength: 30,
          resetStyles: false,
          showSubResults: true,
          sort: { date: "desc" },
        }}
      />
      <div class="mr-2 pb-1 pt-4 text-right text-xs dark:prose-invert">
        Press <span class="prose text-xs dark:prose-invert"
          ><kbd class="">Esc</kbd></span
        > or click anywhere to close
      </div>
    </div>
  </div>
</aside>

<script is:inline>
  const magnifyingGlass = document.getElementById("magnifying-glass");
  const backdrop = document.getElementById("backdrop");

  function openPagefind() {
    const searchDiv = document.getElementById("search");
    const search = searchDiv.querySelector("input");
    setTimeout(() => {
      search.focus();
    }, 0);
    backdrop?.classList.remove("invisible");
    backdrop?.classList.add("visible");
  }

  function showSearchLoading() {
    const loading = document.getElementById("search-loading");
    const searchContainer = document.getElementById("search");
    loading?.classList.remove("hidden");
    searchContainer?.classList.add("opacity-50");
  }

  function hideSearchLoading() {
    const loading = document.getElementById("search-loading");
    const searchContainer = document.getElementById("search");
    loading?.classList.add("hidden");
    searchContainer?.classList.remove("opacity-50");
  }

  function closePagefind() {
    const search = document.getElementById("search");
    search.value = "";
    backdrop?.classList.remove("visible");
    backdrop?.classList.add("invisible");
  }

  // open pagefind
  magnifyingGlass?.addEventListener("click", () => {
    openPagefind();
  });

  document.addEventListener("keydown", (e) => {
    if (e.key === "/") {
      e.preventDefault();
      openPagefind();
    } else if ((e.metaKey || e.ctrlKey) && e.key === "k") {
      e.preventDefault();
      openPagefind();
    }
  });

  // close pagefind
  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape") {
      closePagefind();
    }
  });

  // close pagefind when searched result(link) clicked
  document.addEventListener("click", (event) => {
    if (event.target.classList.contains("pagefind-ui__result-link")) {
      closePagefind();
    }
  });

  backdrop?.addEventListener("click", (event) => {
    if (!event.target.closest("#pagefind-container")) {
      closePagefind();
    }
  });

  // Add search loading functionality
  document.addEventListener("DOMContentLoaded", () => {
    const searchInput = document.querySelector("#search input");
    if (searchInput) {
      let searchTimeout;

      searchInput.addEventListener("input", (e) => {
        const query = e.target.value.trim();

        if (query.length > 0) {
          showSearchLoading();

          // Clear previous timeout
          clearTimeout(searchTimeout);

          // Hide loading after a short delay (pagefind is usually very fast)
          searchTimeout = setTimeout(() => {
            hideSearchLoading();
          }, 800);
        } else {
          hideSearchLoading();
        }
      });
    }
  });

  // prevent form submission
  const form = document.getElementById("form");
  form?.addEventListener("submit", (event) => {
    event.preventDefault();
  });
</script>

<style is:global>
  :root {
    --pagefind-ui-scale: 0.75;
    --pagefind-ui-border-width: 1px;
    --pagefind-ui-border-radius: 3px;
    --pagefind-ui-font: "Geist", sans-serif;
    --pagefind-ui-primary: #3d3d3d;
    --pagefind-ui-text: #3d3d3d;
    --pagefind-ui-background: #ffffff;
    --pagefind-ui-border: #d0d0d0;
    --pagefind-ui-tag: #f5f5f5;
  }

  .dark {
    --pagefind-ui-primary: #d4d4d4;
    --pagefind-ui-text: #d4d4d4;
    --pagefind-ui-background: #171717;
    --pagefind-ui-border: #404040;
  }

  #search input {
    font-weight: normal;
  }

  #search p {
    font-weight: normal;
  }

  #search .pagefind-ui__result-title {
    font-weight: 600;
  }

  #search .pagefind-ui__message {
    padding: 0;
    padding-bottom: 0.75rem;
  }
</style>
