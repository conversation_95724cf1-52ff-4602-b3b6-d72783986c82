---
import Throbbers from "./Throbbers.astro";

interface Props {
  src: string;
  alt: string;
  class?: string;
  width?: number;
  height?: number;
}

const { src, alt, class: className = "", width, height } = Astro.props;
const imageId = `img-${Math.random().toString(36).substr(2, 9)}`;
---

<div class={`relative ${className}`}>
  <!-- Loading State -->
  <div 
    id={`loader-${imageId}`}
    class="absolute inset-0 flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded"
  >
    <div class="text-center">
      <Throbbers type="pulse" size="md" color="#89B4FA" />
      <p class="mt-2 text-xs text-neutral-600 dark:text-neutral-400">Loading image...</p>
    </div>
  </div>
  
  <!-- Actual Image -->
  <img 
    id={imageId}
    src={src} 
    alt={alt} 
    width={width}
    height={height}
    class="w-full h-full object-cover opacity-0 transition-opacity duration-500 rounded"
    loading="lazy"
  />
</div>

<script define:vars={{ imageId }}>
  document.addEventListener('DOMContentLoaded', () => {
    const img = document.getElementById(imageId);
    const loader = document.getElementById(`loader-${imageId}`);
    
    if (img && loader) {
      img.addEventListener('load', () => {
        img.style.opacity = '1';
        loader.style.display = 'none';
      });
      
      img.addEventListener('error', () => {
        loader.innerHTML = `
          <div class="text-center text-red-500">
            <svg class="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <p class="text-xs">Failed to load image</p>
          </div>
        `;
      });
    }
  });
</script>
