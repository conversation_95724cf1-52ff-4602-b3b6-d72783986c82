---
import Throbbers from "./Throbbers.astro";

interface Props {
  title?: string;
  steps?: Array<{name: string, description: string}>;
}

const { 
  title = "ETL Pipeline Demo", 
  steps = [
    { name: "Extract", description: "Extracting data from API..." },
    { name: "Transform", description: "Transforming and cleaning data..." },
    { name: "Load", description: "Loading data to database..." },
    { name: "Validate", description: "Validating data integrity..." }
  ]
} = Astro.props;

const pipelineId = `pipeline-${Math.random().toString(36).substr(2, 9)}`;
---

<div class="pipeline-demo border border-black/15 dark:border-white/20 rounded-lg p-6 my-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-neutral-800 dark:to-neutral-700">
  <div class="flex justify-between items-center mb-6">
    <h3 class="font-semibold text-lg text-black dark:text-white">{title}</h3>
    <button 
      id={`start-${pipelineId}`}
      class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors duration-200 flex items-center gap-2"
    >
      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
      </svg>
      <span id={`start-text-${pipelineId}`}>Start Pipeline</span>
    </button>
  </div>
  
  <div class="space-y-4">
    {steps.map((step, index) => (
      <div 
        id={`step-${pipelineId}-${index}`}
        class="pipeline-step flex items-center space-x-4 p-3 rounded-lg bg-white/50 dark:bg-neutral-800/50 opacity-30 transition-all duration-500"
      >
        <div class="step-indicator flex-shrink-0">
          <div class="w-8 h-8 rounded-full bg-neutral-200 dark:bg-neutral-600 flex items-center justify-center">
            <span class="text-sm font-medium text-neutral-600 dark:text-neutral-400">{index + 1}</span>
          </div>
        </div>
        <div class="flex-1">
          <h4 class="font-medium text-black dark:text-white">{step.name}</h4>
          <p class="text-sm text-neutral-600 dark:text-neutral-400">{step.description}</p>
        </div>
        <div class="step-throbber hidden">
          <Throbbers type="dots" size="sm" color="#3B82F6" />
        </div>
        <div class="step-complete hidden">
          <svg class="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
    ))}
  </div>
  
  <div id={`pipeline-complete-${pipelineId}`} class="hidden mt-6 p-4 bg-green-100 dark:bg-green-900/30 rounded-lg border border-green-200 dark:border-green-800">
    <div class="flex items-center gap-3">
      <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
      <div>
        <h4 class="font-medium text-green-800 dark:text-green-200">Pipeline Complete!</h4>
        <p class="text-sm text-green-600 dark:text-green-300">All steps executed successfully.</p>
      </div>
    </div>
  </div>
</div>

<script define:vars={{ pipelineId, steps }}>
  document.addEventListener('DOMContentLoaded', () => {
    const startButton = document.getElementById(`start-${pipelineId}`);
    const startText = document.getElementById(`start-text-${pipelineId}`);
    const completeMessage = document.getElementById(`pipeline-complete-${pipelineId}`);
    
    if (startButton) {
      startButton.addEventListener('click', async () => {
        // Disable button
        startButton.disabled = true;
        startButton.classList.add('opacity-50', 'cursor-not-allowed');
        startText.textContent = 'Running...';
        
        // Hide complete message
        completeMessage?.classList.add('hidden');
        
        // Reset all steps
        steps.forEach((_, index) => {
          const step = document.getElementById(`step-${pipelineId}-${index}`);
          const throbber = step?.querySelector('.step-throbber');
          const complete = step?.querySelector('.step-complete');
          
          step?.classList.remove('opacity-100');
          step?.classList.add('opacity-30');
          throbber?.classList.add('hidden');
          complete?.classList.add('hidden');
        });
        
        // Execute steps sequentially
        for (let i = 0; i < steps.length; i++) {
          const step = document.getElementById(`step-${pipelineId}-${i}`);
          const throbber = step?.querySelector('.step-throbber');
          const complete = step?.querySelector('.step-complete');
          
          // Activate current step
          step?.classList.remove('opacity-30');
          step?.classList.add('opacity-100');
          throbber?.classList.remove('hidden');
          
          // Simulate step execution time
          const executionTime = Math.random() * 2000 + 1000; // 1-3 seconds
          await new Promise(resolve => setTimeout(resolve, executionTime));
          
          // Complete current step
          throbber?.classList.add('hidden');
          complete?.classList.remove('hidden');
        }
        
        // Show completion message
        completeMessage?.classList.remove('hidden');
        
        // Re-enable button
        startButton.disabled = false;
        startButton.classList.remove('opacity-50', 'cursor-not-allowed');
        startText.textContent = 'Run Again';
      });
    }
  });
</script>
