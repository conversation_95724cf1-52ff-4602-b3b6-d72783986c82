---
import "../styles/global.css";

import "@fontsource/geist-sans/100.css";
import "@fontsource/geist-sans/200.css";
import "@fontsource/geist-sans/300.css";
import "@fontsource/geist-sans/400.css";
import "@fontsource/geist-sans/500.css";
import "@fontsource/geist-sans/600.css";
import "@fontsource/geist-sans/700.css";
import "@fontsource/geist-sans/800.css";
import "@fontsource/geist-sans/900.css";
import "@fontsource/geist-mono/100.css";
import "@fontsource/geist-mono/200.css";
import "@fontsource/geist-mono/300.css";
import "@fontsource/geist-mono/400.css";
import "@fontsource/geist-mono/500.css";
import "@fontsource/geist-mono/600.css";
import "@fontsource/geist-mono/700.css";
import "@fontsource/geist-mono/800.css";
import "@fontsource/geist-mono/900.css";

import { CONTACT, GLOBAL } from "../consts";
import { resolvePath } from "@lib/utils";

interface Props {
  title: string;
  description?: string;
  ogImage?: string;
}

const { title, description, ogImage = "/og-image.png" } = Astro.props;

const canonicalUrl = new URL(Astro.url.pathname, Astro.site);

const twitterHandle = CONTACT.find(
  (contact) => contact.type === "X",
)?.displayAs;

const ogImageFullUrl = new URL(resolvePath(ogImage), Astro.site);
---

<!-- Global Metadata -->
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="icon" type="image/svg+xml" href={resolvePath("/favicon.svg")} />
<meta name="generator" content={Astro.generator} />

<!-- Canonical URL -->
<link rel="canonical" href={canonicalUrl} />

<!-- RSS -->
<link
  rel="alternate"
  type="application/rss+xml"
  title={title}
  href={resolvePath("/feed.xml")}
/>

<!-- Primary Meta Tags -->
<title>{title}</title>
<meta name="title" content={title} />
<meta name="description" content={description ?? GLOBAL.description} />
<meta name="author" content={GLOBAL.author} />

<!-- Open Graph / Facebook / LinkedIn -->
<meta property="og:type" content="article" />
<meta property="og:url" content={canonicalUrl} />
<meta property="og:title" content={title} />
{description && <meta property="og:description" content={description} />}
<meta property="og:image" content={ogImageFullUrl} />

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image" />
<meta property="twitter:url" content={canonicalUrl} />
<meta property="twitter:title" content={title} />
<meta property="twitter:image" content={ogImageFullUrl} />
{description && <meta property="twitter:description" content={description} />}
{twitterHandle && <meta property="twitter:site" content={twitterHandle} />}

<!-- PageFind -->
<link href={resolvePath("/pagefind/pagefind-ui.css")} rel="stylesheet" />
<script is:inline src={resolvePath("/pagefind/pagefind-ui.js")}></script>

<!-- Logo -->
{
  GLOBAL.logo && (
    <div
      id="logo-srcs"
      style="display: none;"
      data-dark-src={resolvePath(GLOBAL.logo.darkThemeSrc, Astro.url.pathname)}
      data-light-src={resolvePath(
        GLOBAL.logo.lightThemeSrc,
        Astro.url.pathname,
      )}
    />
  )
}

<script is:inline>
  function init() {
    preloadTheme();
    onScroll();
    animate();
    updateThemeButtons();
    addCopyCodeButtons();
    setGiscusTheme();

    const backToTop = document.getElementById("back-to-top");
    backToTop?.addEventListener("click", (event) => scrollToTop(event));

    const backToPrev = document.getElementById("back-to-prev");
    backToPrev?.addEventListener("click", () => window.history.back());

    const lightThemeButton = document.getElementById("light-theme-button");
    lightThemeButton?.addEventListener("click", () => {
      localStorage.setItem("theme", "light");
      toggleTheme(false);
      updateThemeButtons();
    });

    const darkThemeButton = document.getElementById("dark-theme-button");
    darkThemeButton?.addEventListener("click", () => {
      localStorage.setItem("theme", "dark");
      toggleTheme(true);
      updateThemeButtons();
    });

    const systemThemeButton = document.getElementById("system-theme-button");
    systemThemeButton?.addEventListener("click", () => {
      localStorage.setItem("theme", "system");
      toggleTheme(window.matchMedia("(prefers-color-scheme: dark)").matches);
      updateThemeButtons();
    });

    window
      .matchMedia("(prefers-color-scheme: dark)")
      .addEventListener("change", (event) => {
        if (localStorage.theme === "system") {
          toggleTheme(event.matches);
        }
      });

    document.addEventListener("scroll", onScroll);
  }

  function updateThemeButtons() {
    const theme = localStorage.getItem("theme");
    const lightThemeButton = document.getElementById("light-theme-button");
    const darkThemeButton = document.getElementById("dark-theme-button");
    const systemThemeButton = document.getElementById("system-theme-button");

    function removeActiveButtonTheme(button) {
      button?.classList.remove("bg-black/5");
      button?.classList.remove("dark:bg-white/5");
    }

    function addActiveButtonTheme(button) {
      button?.classList.add("bg-black/5");
      button?.classList.add("dark:bg-white/5");
    }

    removeActiveButtonTheme(lightThemeButton);
    removeActiveButtonTheme(darkThemeButton);
    removeActiveButtonTheme(systemThemeButton);

    if (theme === "light") {
      addActiveButtonTheme(lightThemeButton);
    } else if (theme === "dark") {
      addActiveButtonTheme(darkThemeButton);
    } else {
      addActiveButtonTheme(systemThemeButton);
    }
  }

  function animate() {
    const animateElements = document.querySelectorAll(".animate");

    animateElements.forEach((element, index) => {
      setTimeout(() => {
        element.classList.add("show");
      }, index * 100);
    });
  }

  function onScroll() {
    if (window.scrollY > 0) {
      document.documentElement.classList.add("scrolled");
    } else {
      document.documentElement.classList.remove("scrolled");
    }
  }

  function scrollToTop(event) {
    event.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }

  function getLogoSrc(darkTheme) {
    const logoSrcs = document.getElementById("logo-srcs");
    if (!logoSrcs) {
      return undefined;
    }

    if (darkTheme) {
      return (
        logoSrcs.getAttribute("data-dark-src") ??
        logoSrcs.getAttribute("data-light-src")
      );
    }

    if (!darkTheme) {
      return (
        logoSrcs.getAttribute("data-light-src") ??
        logoSrcs.getAttribute("data-dark-src")
      );
    }
  }

  function toggleTheme(dark) {
    const css = document.createElement("style");

    css.appendChild(
      document.createTextNode(
        `* {
             -webkit-transition: none !important;
             -moz-transition: none !important;
             -o-transition: none !important;
             -ms-transition: none !important;
             transition: none !important;
          }
        `,
      ),
    );

    document.head.appendChild(css);

    if (dark) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }

    const logoSrc = getLogoSrc(dark);
    const logo = document.getElementById("logo");

    if (logoSrc && logo) {
      logo.src = logoSrc;
    }

    window.getComputedStyle(css).opacity;
    document.head.removeChild(css);

    setGiscusTheme();
  }

  function preloadTheme() {
    const userTheme = localStorage.theme;

    if (userTheme === "light" || userTheme === "dark") {
      toggleTheme(userTheme === "dark");
    } else {
      toggleTheme(window.matchMedia("(prefers-color-scheme: dark)").matches);
    }
  }

  function addCopyCodeButtons() {
    let copyButtonLabel = "📋";
    let codeBlocks = Array.from(document.querySelectorAll("pre"));

    async function copyCode(codeBlock, copyButton) {
      const codeText = codeBlock.innerText;
      const buttonText = copyButton.innerText;
      const textToCopy = codeText.replace(buttonText, "");

      await navigator.clipboard.writeText(textToCopy);
      copyButton.innerText = "✅";

      setTimeout(() => {
        copyButton.innerText = copyButtonLabel;
      }, 2000);
    }

    for (let codeBlock of codeBlocks) {
      const wrapper = document.createElement("div");
      wrapper.style.position = "relative";

      const copyButton = document.createElement("button");
      copyButton.innerText = copyButtonLabel;
      copyButton.classList = "copy-code";

      codeBlock.setAttribute("tabindex", "0");
      codeBlock.appendChild(copyButton);

      codeBlock.parentNode.insertBefore(wrapper, codeBlock);
      wrapper.appendChild(codeBlock);

      copyButton?.addEventListener("click", async () => {
        await copyCode(codeBlock, copyButton);
      });
    }
  }

  const setGiscusTheme = () => {
    const giscus = document.querySelector(".giscus-frame");

    const isDark = document.documentElement.classList.contains("dark");

    if (giscus) {
      const url = new URL(giscus.src);
      url.searchParams.set("theme", isDark ? "dark" : "light");
      giscus.src = url.toString();
    }
  };

  document.addEventListener("DOMContentLoaded", () => init());
  document.addEventListener("astro:after-swap", () => init());
  preloadTheme();
</script>
