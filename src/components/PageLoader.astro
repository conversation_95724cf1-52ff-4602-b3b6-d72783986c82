---
import Throbbers from "./Throbbers.astro";
---

<div id="page-loader" class="hidden fixed inset-0 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm z-50 flex items-center justify-center">
  <div class="text-center">
    <Throbbers type="orbit" size="lg" color="#89B4FA" />
    <p class="mt-4 text-lg font-medium text-black dark:text-white">Loading...</p>
    <p class="text-sm text-neutral-600 dark:text-neutral-400">Please wait</p>
  </div>
</div>

<script is:inline>
  // Show loader on navigation
  document.addEventListener('astro:before-preparation', () => {
    const loader = document.getElementById('page-loader');
    if (loader) {
      loader.classList.remove('hidden');
    }
  });
  
  // Hide loader after page swap
  document.addEventListener('astro:after-swap', () => {
    const loader = document.getElementById('page-loader');
    if (loader) {
      loader.classList.add('hidden');
    }
  });
  
  // Hide loader on page load (for initial page load)
  document.addEventListener('DOMContentLoaded', () => {
    const loader = document.getElementById('page-loader');
    if (loader) {
      loader.classList.add('hidden');
    }
  });
  
  // Global functions for manual control
  window.showPageLoader = (message = 'Loading...') => {
    const loader = document.getElementById('page-loader');
    const text = loader?.querySelector('p');
    if (text) text.textContent = message;
    loader?.classList.remove('hidden');
  };
  
  window.hidePageLoader = () => {
    const loader = document.getElementById('page-loader');
    loader?.classList.add('hidden');
  };
</script>
