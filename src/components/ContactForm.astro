---
import Throbbers from "./Throbbers.astro";
---

<form id="contact-form" class="space-y-6 max-w-md mx-auto p-6 border border-black/15 dark:border-white/20 rounded-lg bg-white dark:bg-neutral-800">
  <div>
    <label for="name" class="block text-sm font-medium text-black dark:text-white mb-2">
      Name
    </label>
    <input 
      type="text" 
      id="name" 
      name="name"
      required 
      class="w-full p-3 border border-black/15 dark:border-white/20 rounded bg-white dark:bg-neutral-700 text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
      placeholder="Your name"
    />
  </div>
  
  <div>
    <label for="email" class="block text-sm font-medium text-black dark:text-white mb-2">
      Email
    </label>
    <input 
      type="email" 
      id="email" 
      name="email"
      required 
      class="w-full p-3 border border-black/15 dark:border-white/20 rounded bg-white dark:bg-neutral-700 text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
      placeholder="<EMAIL>"
    />
  </div>
  
  <div>
    <label for="subject" class="block text-sm font-medium text-black dark:text-white mb-2">
      Subject
    </label>
    <input 
      type="text" 
      id="subject" 
      name="subject"
      required 
      class="w-full p-3 border border-black/15 dark:border-white/20 rounded bg-white dark:bg-neutral-700 text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
      placeholder="What's this about?"
    />
  </div>
  
  <div>
    <label for="message" class="block text-sm font-medium text-black dark:text-white mb-2">
      Message
    </label>
    <textarea 
      id="message" 
      name="message"
      rows="5"
      required 
      class="w-full p-3 border border-black/15 dark:border-white/20 rounded bg-white dark:bg-neutral-700 text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical"
      placeholder="Your message..."
    ></textarea>
  </div>
  
  <button 
    type="submit" 
    id="submit-btn" 
    class="w-full bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded font-medium transition-colors duration-200 flex items-center justify-center gap-2"
  >
    <span id="submit-text">Send Message</span>
    <span id="submit-loading" class="hidden">
      <Throbbers type="spin" size="xs" color="white" />
    </span>
  </button>
  
  <!-- Success Message -->
  <div id="success-message" class="hidden p-4 bg-green-100 dark:bg-green-900/30 rounded border border-green-200 dark:border-green-800">
    <div class="flex items-center gap-3">
      <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
      <p class="text-green-800 dark:text-green-200 font-medium">Message sent successfully!</p>
    </div>
  </div>
  
  <!-- Error Message -->
  <div id="error-message" class="hidden p-4 bg-red-100 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
    <div class="flex items-center gap-3">
      <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      </svg>
      <p class="text-red-800 dark:text-red-200 font-medium">Failed to send message. Please try again.</p>
    </div>
  </div>
</form>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('contact-form');
    const submitBtn = document.getElementById('submit-btn');
    const submitText = document.getElementById('submit-text');
    const submitLoading = document.getElementById('submit-loading');
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');
    
    if (form && submitBtn) {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Hide previous messages
        successMessage?.classList.add('hidden');
        errorMessage?.classList.add('hidden');
        
        // Show loading state
        submitText?.classList.add('hidden');
        submitLoading?.classList.remove('hidden');
        submitBtn.disabled = true;
        submitBtn.classList.add('opacity-75', 'cursor-not-allowed');
        
        try {
          // Get form data
          const formData = new FormData(form);
          const data = Object.fromEntries(formData.entries());
          
          // Simulate form submission (replace with actual endpoint)
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // For demo purposes, randomly succeed or fail
          if (Math.random() > 0.3) {
            // Success
            successMessage?.classList.remove('hidden');
            form.reset();
          } else {
            // Error
            throw new Error('Simulated error');
          }
          
        } catch (error) {
          console.error('Error submitting form:', error);
          errorMessage?.classList.remove('hidden');
        } finally {
          // Reset button state
          submitText?.classList.remove('hidden');
          submitLoading?.classList.add('hidden');
          submitBtn.disabled = false;
          submitBtn.classList.remove('opacity-75', 'cursor-not-allowed');
        }
      });
    }
  });
</script>
