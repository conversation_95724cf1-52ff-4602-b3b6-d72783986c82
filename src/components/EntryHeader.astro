---
import FormattedDate from "./FormattedDate.astro";
import { readingTime } from "@lib/utils";
import Tag from "./Tag.astro";

interface Props {
    title: string;
    date: Date;
    lastUpdateDate?: Date;
    description?: string;
    body: string;
    tags?: string[];
}

const { title, date, lastUpdateDate, description, body, tags } = Astro.props;
---

<div class="animate my-10 space-y-1">
    <div class="flex items-center gap-1.5">
        <div class="font-base text-sm">
            <FormattedDate date={date} />
            {
                lastUpdateDate && (
                    <span>
                        {" "}
                        (updated: <FormattedDate date={lastUpdateDate} />)
                    </span>
                )
            }
        </div>
        &bull;
        <div class="font-base text-sm">
            {readingTime(body)}
        </div>
    </div>
    <h1 class="text-3xl font-semibold text-black dark:text-white">
        {title}
    </h1>
    {
        description && (
            <p class="text-gray-600 dark:text-gray-400">{description}</p>
        )
    }
    {
        tags && tags?.length > 0 ? (
            <div class="flex gap-2 pt-1">
                {tags.map((tag) => (
                    <Tag tag={tag} />
                ))}
            </div>
        ) : null
    }
</div>
