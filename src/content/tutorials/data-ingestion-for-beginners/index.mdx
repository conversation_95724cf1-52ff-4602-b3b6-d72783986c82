---
title: "Data Ingestion for Beginners: A Data Engineering Guide"
description: "Learn the fundamentals of data ingestion and build your first data pipeline"
date: "2024-06-25"
lastUpdateDate: "2024-06-25"
ogImage: "/tutorials/data-ingestion-for-beginners/og-image.png"
tags:
  - data-engineering
  - data-ingestion
  - python
  - etl
  - beginners
---

import Callout from "@components/Callout.astro";
import CodeRunner from "@components/CodeRunner.astro";
import MdxPublicImage from "@components/MdxPublicImage.astro";
import PipelineDemo from "@components/PipelineDemo.astro";

# Data Ingestion for Beginners: A Data Engineering Guide

Data is the lifeblood of modern organizations, but raw data is rarely ready for analysis. Data engineering is the discipline that transforms raw data into a format suitable for analysis, and data ingestion is the first critical step in this process. This tutorial will introduce you to data ingestion concepts and guide you through building your first data pipeline.

## What is Data Engineering?

**Data Engineering** is the practice of designing and building systems for collecting, storing, and analyzing data at scale. Data engineers develop the infrastructure that enables data scientists and analysts to access and work with data effectively.

## What is Data Ingestion?

**Data Ingestion** is the process of importing data from various sources into a storage system (like a data warehouse or data lake) where it can be accessed, used, and analyzed. It's the first step in the data pipeline and sets the foundation for all subsequent data processing.

<Callout type="info">
  Think of data ingestion as collecting water from various sources (rivers,
  rain, wells) and channeling it into a reservoir where it can be processed,
  filtered, and distributed.
</Callout>

## Data Ingestion Concepts for Beginners

Before diving into the technical details, let's understand some key concepts:

### Types of Data Sources

Data can come from many sources, including:

1. **Databases**: SQL databases (MySQL, PostgreSQL), NoSQL databases (MongoDB, Cassandra)
2. **APIs**: Web services that provide data through HTTP requests
3. **Files**: CSV, JSON, XML, Excel files
4. **Streaming Sources**: Real-time data from IoT devices, social media, or user interactions
5. **Web Scraping**: Extracting data from websites

### Batch vs. Streaming Ingestion

There are two primary approaches to data ingestion:

1. **Batch Ingestion**: Data is collected over a period and processed in groups or "batches." This is like collecting rainwater in a barrel and processing it once the barrel is full.

   - **Pros**: Simpler to implement, efficient for large volumes of historical data
   - **Cons**: Introduces latency, not suitable for real-time analytics

2. **Streaming Ingestion**: Data is processed continuously as it's generated. This is like processing rainwater as it falls.
   - **Pros**: Near real-time data availability, good for time-sensitive applications
   - **Cons**: More complex to implement, requires different tools and architecture

### ETL vs. ELT

Two common patterns for processing ingested data:

1. **ETL (Extract, Transform, Load)**: Data is transformed before being loaded into the target system.

   - **Extract**: Get data from source
   - **Transform**: Clean, validate, and restructure the data
   - **Load**: Store the transformed data in the target system

2. **ELT (Extract, Load, Transform)**: Data is loaded into the target system before transformation.
   - **Extract**: Get data from source
   - **Load**: Store the raw data in the target system
   - **Transform**: Transform the data within the target system

<Callout type="tip">
  For beginners, ETL is often easier to understand and implement. As you
  advance, you might explore ELT for certain use cases.
</Callout>

## Setting Up Your Environment

Let's set up a Python environment for data ingestion. We'll use Python because it's beginner-friendly and has excellent libraries for data engineering.

### Prerequisites

- Python 3.8+ installed
- Basic knowledge of Python
- A text editor or IDE (like VS Code, PyCharm)
- Terminal/Command Prompt access

### Creating a Virtual Environment

It's a good practice to create a virtual environment for your projects:

```bash
# Create a directory for your project
mkdir data-ingestion-tutorial
cd data-ingestion-tutorial

# Create a virtual environment
python -m venv venv

# Activate the virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### Installing Required Libraries

Install the libraries we'll need for this tutorial:

```bash
pip install pandas requests sqlalchemy jupyter matplotlib
```

These libraries provide:

- `pandas`: Data manipulation and analysis
- `requests`: Making HTTP requests to APIs
- `sqlalchemy`: Database interactions
- `jupyter`: Interactive notebook environment
- `matplotlib`: Data visualization

## Your First Data Ingestion Pipeline

Let's build a simple data ingestion pipeline that:

1. Extracts data from a public API
2. Transforms the data into a usable format
3. Loads the data into a SQLite database

### Step 1: Extract Data from an API

Create a file named `extract.py`:

```python
import requests
import pandas as pd
import json
from datetime import datetime

def extract_weather_data(city="London"):
    """
    Extract current weather data for a given city using the OpenWeatherMap API.
    """
    # Note: In a real project, you would store this in an environment variable
    api_key = "YOUR_API_KEY"  # Get a free API key from openweathermap.org

    # For this tutorial, we'll use a sample response if no API key is provided
    if api_key == "YOUR_API_KEY":
        print("Using sample data since no API key was provided.")
        # Sample data for demonstration
        sample_data = {
            "main": {
                "temp": 15.2,
                "feels_like": 14.8,
                "humidity": 76
            },
            "wind": {
                "speed": 3.6
            },
            "weather": [
                {
                    "main": "Clouds",
                    "description": "scattered clouds"
                }
            ],
            "name": city
        }
        return sample_data

    # If you have an API key, use the actual API
    url = f"http://api.openweathermap.org/data/2.5/weather?q={city}&appid={api_key}&units=metric"

    try:
        response = requests.get(url)
        response.raise_for_status()  # Raise an exception for HTTP errors
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data: {e}")
        return None

def extract_data_for_multiple_cities(cities=["London", "New York", "Tokyo", "Sydney", "Paris"]):
    """
    Extract weather data for multiple cities and return as a DataFrame.
    """
    data_list = []

    for city in cities:
        raw_data = extract_weather_data(city)
        if raw_data:
            # Extract the relevant fields
            data_list.append({
                "city": raw_data["name"],
                "temperature": raw_data["main"]["temp"],
                "feels_like": raw_data["main"]["feels_like"],
                "humidity": raw_data["main"]["humidity"],
                "wind_speed": raw_data["wind"]["speed"],
                "weather_condition": raw_data["weather"][0]["main"],
                "weather_description": raw_data["weather"][0]["description"],
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })

    # Convert to DataFrame
    return pd.DataFrame(data_list)

if __name__ == "__main__":
    # Test the extraction
    df = extract_data_for_multiple_cities()
    print(df)
```

<CodeRunner
  code={`import requests
import pandas as pd
from datetime import datetime

def extract_weather_data(city="London"): # Simulate API response (in real scenario, use actual API)
mock_data = {
"name": city,
"main": {"temp": 15.2, "humidity": 76},
"weather": [{"main": "Clouds", "description": "overcast clouds"}],
"wind": {"speed": 3.5}
}

    # Transform to DataFrame
    df = pd.DataFrame([{
        'city': mock_data['name'],
        'temperature': mock_data['main']['temp'],
        'humidity': mock_data['main']['humidity'],
        'weather': mock_data['weather'][0]['main'],
        'wind_speed': mock_data['wind']['speed'],
        'timestamp': datetime.now()
    }])

    return df

# Try it out

weather_df = extract_weather_data("London")
print(weather_df)`}
  language="python"
  title="Try: Extract Weather Data"
  output={` city temperature humidity weather wind_speed timestamp
0 London 15.2 76 Clouds 3.5 2024-01-15 10:30:45`}
/>

### Step 2: Transform the Data

Create a file named `transform.py`:

```python
import pandas as pd

def transform_weather_data(df):
    """
    Transform the weather data:
    1. Convert temperature from Celsius to Fahrenheit
    2. Categorize weather conditions
    3. Create a 'wind_category' column
    """
    # Make a copy to avoid modifying the original
    transformed_df = df.copy()

    # Add temperature in Fahrenheit
    transformed_df['temperature_f'] = transformed_df['temperature'] * 9/5 + 32

    # Categorize weather conditions
    weather_categories = {
        'Clear': 'Good',
        'Clouds': 'Moderate',
        'Rain': 'Poor',
        'Snow': 'Poor',
        'Drizzle': 'Moderate',
        'Thunderstorm': 'Severe',
        'Mist': 'Moderate',
        'Smoke': 'Poor',
        'Haze': 'Moderate',
        'Dust': 'Poor',
        'Fog': 'Poor',
        'Sand': 'Poor',
        'Ash': 'Poor',
        'Squall': 'Severe',
        'Tornado': 'Extreme'
    }

    transformed_df['weather_category'] = transformed_df['weather_condition'].map(
        weather_categories).fillna('Unknown')

    # Categorize wind speed
    def categorize_wind(speed):
        if speed < 3.5:
            return 'Low'
        elif speed < 8:
            return 'Moderate'
        elif speed < 13.5:
            return 'High'
        else:
            return 'Very High'

    transformed_df['wind_category'] = transformed_df['wind_speed'].apply(categorize_wind)

    return transformed_df

if __name__ == "__main__":
    # For testing, create a sample DataFrame
    from extract import extract_data_for_multiple_cities

    df = extract_data_for_multiple_cities()
    transformed_df = transform_weather_data(df)
    print(transformed_df)
```

### Step 3: Load the Data

Create a file named `load.py`:

```python
import pandas as pd
import sqlite3
from sqlalchemy import create_engine

def load_to_sqlite(df, db_name='weather_data.db', table_name='weather'):
    """
    Load DataFrame to SQLite database.
    """
    # Create a SQLite database engine
    engine = create_engine(f'sqlite:///{db_name}')

    # Load the data to the database
    df.to_sql(table_name, engine, if_exists='append', index=False)

    print(f"Data loaded to {db_name}, table: {table_name}")

    # Verify the data was loaded
    conn = sqlite3.connect(db_name)
    result = pd.read_sql(f"SELECT * FROM {table_name} ORDER BY timestamp DESC LIMIT 5", conn)
    conn.close()

    return result

if __name__ == "__main__":
    # For testing, create and transform sample data
    from extract import extract_data_for_multiple_cities
    from transform import transform_weather_data

    df = extract_data_for_multiple_cities()
    transformed_df = transform_weather_data(df)

    # Load to database
    result = load_to_sqlite(transformed_df)
    print("\nVerification - Last 5 records:")
    print(result)
```

### Step 4: Create the Pipeline

Now, let's tie everything together in a file named `pipeline.py`:

```python
from extract import extract_data_for_multiple_cities
from transform import transform_weather_data
from load import load_to_sqlite
import time
import matplotlib.pyplot as plt
import pandas as pd
import sqlite3

def run_pipeline(cities=None):
    """
    Run the complete ETL pipeline.
    """
    print("Starting data ingestion pipeline...")

    # Extract
    print("\n--- Extraction Phase ---")
    if cities:
        df = extract_data_for_multiple_cities(cities)
    else:
        df = extract_data_for_multiple_cities()
    print(f"Extracted data for {len(df)} cities")

    # Transform
    print("\n--- Transformation Phase ---")
    transformed_df = transform_weather_data(df)
    print("Data transformed successfully")

    # Load
    print("\n--- Loading Phase ---")
    result = load_to_sqlite(transformed_df)
    print("Data loaded successfully")

    return transformed_df

def visualize_data(db_name='weather_data.db'):
    """
    Create a simple visualization of the collected data.
    """
    conn = sqlite3.connect(db_name)
    df = pd.read_sql("SELECT * FROM weather", conn)
    conn.close()

    if df.empty:
        print("No data to visualize.")
        return

    # Create a bar chart of temperatures by city
    plt.figure(figsize=(10, 6))
    plt.bar(df['city'], df['temperature'], color='skyblue')
    plt.xlabel('City')
    plt.ylabel('Temperature (°C)')
    plt.title('Current Temperature by City')
    plt.xticks(rotation=45)
    plt.tight_layout()

    # Save the plot
    plt.savefig('temperature_by_city.png')
    print("Visualization saved as 'temperature_by_city.png'")

    # Show the plot if running in an interactive environment
    plt.show()

if __name__ == "__main__":
    # Run the pipeline
    transformed_df = run_pipeline()

    # Visualize the data
    visualize_data()

    print("\nPipeline execution completed successfully!")
```

### Step 5: Run the Pipeline

Run the pipeline from your terminal:

```bash
python pipeline.py
```

You should see output showing the extraction, transformation, and loading of weather data, followed by a visualization of the temperatures.

## Understanding What We've Built

Let's break down what our data ingestion pipeline does:

1. **Extract**: We fetch weather data from an API (or use sample data) for multiple cities.
2. **Transform**: We convert temperatures, categorize weather conditions, and add wind categories.
3. **Load**: We store the transformed data in a SQLite database.
4. **Visualize**: We create a simple bar chart to visualize the temperature data.

This is a basic ETL pipeline that demonstrates the core concepts of data ingestion.

<PipelineDemo
  title="Weather Data ETL Pipeline"
  steps={[
    {
      name: "Extract",
      description: "Fetching weather data from OpenWeatherMap API...",
    },
    {
      name: "Transform",
      description: "Converting temperature units and cleaning data...",
    },
    {
      name: "Load",
      description: "Storing processed data in SQLite database...",
    },
    { name: "Validate", description: "Running data quality checks..." },
  ]}
/>

## Common Data Ingestion Tools

While we built a simple pipeline with Python, there are many specialized tools for data ingestion:

### Open-Source Tools

1. **Apache Airflow**: A platform to programmatically author, schedule, and monitor workflows
2. **Apache NiFi**: A system to automate the flow of data between systems
3. **Apache Kafka**: A distributed streaming platform for building real-time data pipelines
4. **Logstash**: Part of the ELK stack, used for collecting, processing, and forwarding events and logs

### Cloud-Based Services

1. **AWS Glue**: A fully managed ETL service
2. **Google Cloud Dataflow**: A unified stream and batch data processing service
3. **Azure Data Factory**: A cloud-based data integration service
4. **Fivetran**: A fully managed data integration service

<Callout type="tip">
  For beginners, start with Python-based solutions to understand the concepts.
  As you grow, explore specialized tools that can handle larger volumes and more
  complex scenarios.
</Callout>

## Best Practices for Data Ingestion

As you continue your data engineering journey, keep these best practices in mind:

### 1. Data Quality Checks

Always validate your data during ingestion:

- Check for missing values
- Validate data types
- Ensure data falls within expected ranges
- Look for duplicates

### 2. Error Handling

Robust error handling is crucial:

- Log all errors
- Implement retry mechanisms for transient failures
- Have fallback strategies for critical pipelines

### 3. Monitoring and Alerting

Keep an eye on your pipelines:

- Monitor pipeline execution times
- Set up alerts for failures
- Track data volumes and anomalies

### 4. Documentation

Document everything:

- Data sources and their schemas
- Transformation logic
- Loading procedures
- Dependencies between components

### 5. Incremental Loading

For efficiency, implement incremental loading:

- Only process new or changed data
- Use timestamps or change tracking mechanisms
- Maintain state between pipeline runs

## Common Challenges in Data Ingestion

Be prepared to face these common challenges:

1. **Data Volume**: Handling large volumes of data efficiently
2. **Data Variety**: Dealing with different data formats and structures
3. **Data Velocity**: Processing data that arrives at high speed
4. **Schema Changes**: Adapting to changes in source data structures
5. **Resource Constraints**: Managing CPU, memory, and network resources

## Next Steps in Your Data Engineering Journey

Now that you've built your first data ingestion pipeline, here are some ways to expand your knowledge:

1. **Add More Data Sources**: Try ingesting data from databases, files, or web scraping
2. **Implement Scheduling**: Use tools like cron or Airflow to schedule your pipeline
3. **Explore Streaming**: Learn about real-time data ingestion with Kafka or similar tools
4. **Scale Up**: Move from SQLite to a more robust database like PostgreSQL
5. **Add More Transformations**: Implement more complex data cleaning and enrichment

## Adding a Browser-Based Content Management System to Your Static Site

Creating content directly from your browser can significantly streamline your blogging workflow. Let's explore how to add a browser-based content management system (CMS) to your static site hosted on GitHub Pages.

### Why Add Browser-Based Content Creation?

- **Convenience**: Create and edit content from any device with a browser
- **No local development environment needed**: Write posts without installing development tools
- **Collaboration**: Allow multiple authors to contribute more easily
- **Immediate preview**: See how your content will look before publishing

### Options for Browser-Based Content Management

Here are several approaches to enable browser-based content creation for your static site:

#### 1. Headless CMS with GitHub Integration

A headless CMS provides a user-friendly interface for content creation while storing the content in your GitHub repository.

**Top options that work well with GitHub Pages:**

##### Forestry.io (now TinaCMS)

[TinaCMS](https://tina.io/) provides a visual editing experience that commits directly to your GitHub repository.

**Setup steps:**

1. Sign up for TinaCMS
2. Connect your GitHub repository
3. Configure your content schema
4. Use the visual editor to create content

```javascript
// Example TinaCMS configuration (tinacms.config.js)
export default defineConfig({
  branch: "main",
  clientId: "your-client-id",
  token: "your-token",
  build: {
    outputFolder: "admin",
    publicFolder: "public",
  },
  schema: {
    collections: [
      {
        name: "post",
        label: "Blog Posts",
        path: "content/posts",
        format: "mdx",
        fields: [
          {
            type: "string",
            name: "title",
            label: "Title",
            required: true,
          },
          {
            type: "rich-text",
            name: "body",
            label: "Body",
            isBody: true,
          },
        ],
      },
    ],
  },
});
```

##### Netlify CMS

[Netlify CMS](https://www.netlifycms.org/) is an open-source CMS that works with any static site generator and stores content in your Git repository.

**Setup steps:**

1. Add the Netlify CMS package to your site
2. Create a configuration file
3. Set up authentication (typically using Netlify Identity)

```yaml
# Example Netlify CMS configuration (public/admin/config.yml)
backend:
  name: github
  repo: username/repo-name
  branch: main
  auth_endpoint: api/auth # For custom authentication

media_folder: "public/images/uploads"
public_folder: "/images/uploads"

collections:
  - name: "blog"
    label: "Blog Posts"
    folder: "src/content/blog"
    create: true
    slug: "{{year}}-{{month}}-{{day}}-{{slug}}"
    fields:
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Description", name: "description", widget: "text" }
      - { label: "Publish Date", name: "date", widget: "datetime" }
      - { label: "Tags", name: "tags", widget: "list" }
      - { label: "Body", name: "body", widget: "markdown" }
```

#### 2. GitHub Content Editor Integration

Leverage GitHub's built-in content editing capabilities with custom enhancements.

##### GitHub's Web Editor + Custom Preview

Create a custom preview system that works with GitHub's web-based editor:

1. Set up a GitHub Action that builds a preview when content changes are proposed
2. Create a custom interface that links to GitHub's editor but provides a better preview

```yaml
# GitHub Action for preview builds (.github/workflows/preview.yml)
name: Preview Build

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  build_preview:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm ci
      - run: npm run build
      - uses: actions/upload-artifact@v3
        with:
          name: preview-build
          path: dist
      - name: Deploy Preview
        uses: rossjrw/pr-preview-action@v1
        with:
          source-dir: ./dist/
```

#### 3. Custom Admin Interface with GitHub API

Build a custom admin interface that uses the GitHub API to create and edit content:

```javascript
// Example of using GitHub API to create a new post
async function createPost(title, content, token) {
  const github = new Octokit({ auth: token });

  // Create file content with frontmatter
  const date = new Date().toISOString().split("T")[0];
  const fileName = `${date}-${title.toLowerCase().replace(/\s+/g, "-")}.md`;
  const fileContent = `---\ntitle: "${title}"\ndate: "${date}"\n---\n\n${content}`;

  // Encode content to base64
  const contentEncoded = btoa(fileContent);

  // Create file in repository
  await github.repos.createOrUpdateFileContents({
    owner: "your-username",
    repo: "your-repo",
    path: `content/posts/${fileName}`,
    message: `Add new post: ${title}`,
    content: contentEncoded,
    branch: "main",
  });

  return `Post "${title}" created successfully!`;
}
```

### Database Options for GitHub Pages

Since GitHub Pages only serves static content, you'll need external services to store dynamic data. Here are some options that work well with static sites:

#### 1. GitHub as a Database

Use GitHub itself as your database by storing content as markdown or JSON files:

**Pros:**

- Free with your GitHub account
- Version control built-in
- No additional services needed

**Cons:**

- Limited query capabilities
- Not suitable for high-frequency updates
- Rate limits on the GitHub API

#### 2. Serverless Database Options

These services offer free tiers that work well with static sites:

##### Supabase

[Supabase](https://supabase.com/) provides a PostgreSQL database with a generous free tier:

```javascript
// Example of using Supabase as a database for blog posts
import { createClient } from "@supabase/supabase-js";

const supabase = createClient(
  "https://your-project.supabase.co",
  "your-anon-key",
);

// Create a new post
async function createPost(title, content, authorId) {
  const { data, error } = await supabase
    .from("posts")
    .insert([{ title, content, author_id: authorId, created_at: new Date() }]);

  if (error) throw error;
  return data;
}

// Get all posts
async function getPosts() {
  const { data, error } = await supabase
    .from("posts")
    .select("*")
    .order("created_at", { ascending: false });

  if (error) throw error;
  return data;
}
```

##### Firebase Firestore

[Firebase](https://firebase.google.com/) offers a NoSQL database with a free tier:

```javascript
import { initializeApp } from "firebase/app";
import {
  getFirestore,
  collection,
  addDoc,
  getDocs,
  query,
  orderBy,
} from "firebase/firestore";

const firebaseConfig = {
  // Your Firebase config
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Create a new post
async function createPost(title, content, authorId) {
  try {
    const docRef = await addDoc(collection(db, "posts"), {
      title,
      content,
      authorId,
      createdAt: new Date(),
    });
    return docRef.id;
  } catch (e) {
    console.error("Error adding document: ", e);
    throw e;
  }
}

// Get all posts
async function getPosts() {
  const q = query(collection(db, "posts"), orderBy("createdAt", "desc"));
  const querySnapshot = await getDocs(q);
  const posts = [];
  querySnapshot.forEach((doc) => {
    posts.push({ id: doc.id, ...doc.data() });
  });
  return posts;
}
```

#### 3. FaunaDB

[FaunaDB](https://fauna.com/) is a serverless database with a document-relational model and a generous free tier:

```javascript
import faunadb from "faunadb";
const q = faunadb.query;

const client = new faunadb.Client({
  secret: "your-fauna-secret",
});

// Create a new post
async function createPost(title, content, authorId) {
  try {
    const result = await client.query(
      q.Create(q.Collection("posts"), {
        data: {
          title,
          content,
          authorId,
          createdAt: q.Now(),
        },
      }),
    );
    return result.ref.id;
  } catch (error) {
    console.error("Error creating post:", error);
    throw error;
  }
}

// Get all posts
async function getPosts() {
  try {
    const result = await client.query(
      q.Map(
        q.Paginate(q.Match(q.Index("all_posts")), { size: 100 }),
        q.Lambda("ref", q.Get(q.Var("ref"))),
      ),
    );
    return result.data.map((item) => ({
      id: item.ref.id,
      ...item.data,
    }));
  } catch (error) {
    console.error("Error fetching posts:", error);
    throw error;
  }
}
```

### Implementing a Complete Solution

Here's how to implement a complete browser-based content management system for your static site hosted on GitHub Pages:

#### 1. Set Up Authentication

Use GitHub OAuth to authenticate users:

```javascript
// Example of GitHub OAuth flow
async function initiateGitHubLogin() {
  const clientId = "your-github-client-id";
  const redirectUri = encodeURIComponent("https://your-site.com/auth/callback");
  const scope = encodeURIComponent("repo");

  window.location.href = `https://github.com/login/oauth/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}`;
}

// This would be handled by a serverless function
async function handleAuthCallback(code) {
  const response = await fetch("/.netlify/functions/auth", {
    method: "POST",
    body: JSON.stringify({ code }),
  });

  const { token } = await response.json();
  localStorage.setItem("github_token", token);
  return token;
}
```

#### 2. Create an Admin Interface

Build a simple admin interface for creating and editing posts:

```jsx
// Example React component for post editor
function PostEditor() {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [saving, setSaving] = useState(false);

  async function handleSubmit(e) {
    e.preventDefault();
    setSaving(true);

    try {
      const token = localStorage.getItem("github_token");
      await createPost(title, content, token);
      setTitle("");
      setContent("");
      alert("Post created successfully!");
    } catch (error) {
      console.error("Error creating post:", error);
      alert("Failed to create post. Please try again.");
    } finally {
      setSaving(false);
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label htmlFor="title">Title</label>
        <input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
        />
      </div>
      <div>
        <label htmlFor="content">Content (Markdown)</label>
        <textarea
          id="content"
          value={content}
          onChange={(e) => setContent(e.target.value)}
          rows={10}
          required
        />
      </div>
      <button type="submit" disabled={saving}>
        {saving ? "Saving..." : "Create Post"}
      </button>
    </form>
  );
}
```

#### 3. Set Up a Serverless Function for GitHub API Interaction

Create a serverless function to handle GitHub API calls:

```javascript
// Example Netlify function for creating posts via GitHub API
const { Octokit } = require("@octokit/rest");

exports.handler = async function (event, context) {
  try {
    const { title, content, token } = JSON.parse(event.body);

    if (!token) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: "Authentication required" }),
      };
    }

    const github = new Octokit({ auth: token });

    // Create file content with frontmatter
    const date = new Date().toISOString().split("T")[0];
    const fileName = `${date}-${title.toLowerCase().replace(/\s+/g, "-")}.md`;
    const fileContent = `---\ntitle: "${title}"\ndate: "${date}"\n---\n\n${content}`;

    // Encode content to base64
    const contentEncoded = Buffer.from(fileContent).toString("base64");

    // Create file in repository
    await github.repos.createOrUpdateFileContents({
      owner: "your-username",
      repo: "your-repo",
      path: `content/posts/${fileName}`,
      message: `Add new post: ${title}`,
      content: contentEncoded,
      branch: "main",
    });

    return {
      statusCode: 200,
      body: JSON.stringify({
        success: true,
        message: `Post "${title}" created successfully!`,
      }),
    };
  } catch (error) {
    console.error("Error creating post:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: "Failed to create post",
        details: error.message,
      }),
    };
  }
};
```

<Callout type="info">
  While GitHub Pages is free and great for hosting static content, you'll need
  additional services for the dynamic parts of your CMS. Many services like
  Netlify, Vercel, Supabase, and Firebase offer generous free tiers that work
  well for personal blogs and small projects.
</Callout>

### Recommended Complete Solutions

If you prefer not to build a custom solution, these platforms provide complete browser-based content management for static sites:

1. **Netlify CMS + Netlify**: A complete solution with authentication, content management, and serverless functions

2. **Stackbit**: Visual editing platform that works with multiple static site generators and headless CMS options

3. **Forestry/TinaCMS + Vercel**: Powerful content editing with preview deployments

4. **Contentful + GitHub Actions**: Enterprise-grade headless CMS with automated builds

These solutions allow you to create and edit content directly from your browser while still leveraging the speed, security, and cost benefits of static site hosting on GitHub Pages.

## Conclusion

Data ingestion is the foundation of any data engineering workflow. By understanding how to extract data from sources, transform it into a usable format, and load it into a storage system, you've taken your first step into the world of data engineering.

Remember that data engineering is a vast field, and data ingestion is just the beginning. As you continue learning, you'll discover more advanced concepts and tools that will help you build robust, scalable data pipelines.

<Callout type="info">
  The code in this tutorial is intentionally simplified for educational
  purposes. In a production environment, you would implement more robust error
  handling, configuration management, logging, and testing.
</Callout>

## Additional Resources

- [Pandas Documentation](https://pandas.pydata.org/docs/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [Apache Airflow Documentation](https://airflow.apache.org/docs/)
- [The Data Engineering Cookbook](https://github.com/andkret/Cookbook) by Andreas Kretz
- [Designing Data-Intensive Applications](https://dataintensive.net/) by Martin Kleppmannimport Callout from "@components/Callout.astro";
  import MdxPublicImage from "@components/MdxPublicImage.astro";import Callout from "@components/Callout.astro";
  import MdxPublicImage from "@components/MdxPublicImage.astro";
