---
title: "Everything new in valon"
date: "2024-05-09"
lastUpdateDate: "2025-01-04"
ogImage: "/blog/everything-new-in-valon/article-og-image.png"
description: "valon changelog"
tags:
  - parent-project
---

import Callout from "@/components/Callout.astro";

---

## Support for base path

valon supports the base path, so you can host your site in the nested "subdirectory". Just set the `base` property in the `astro.config.mjs` file.

This is useful when you want to host your site on e.g., GitHub Pages under `https://username.github.io/repo-name/`. In this case, you should set the `base` property to `/repo-name/`.

The `<base>` tag is not used due to the errors it causes in different places.
Most of the time the components use `resolvePath` function from `src/utils.ts` to generate correct paths.

Navigate to the [Content and syntax guide](../content-and-syntax-guide) post to read how to handle [images](../content-and-syntax-guide#public-images) and [links](../content-and-syntax-guide#relative-links).

## Pagination

All collections support pagination by default. On the collection page, you can navigate between pages using the pagination component at the bottom.
The number of items per page is configurable in the `src/consts.ts` file (individually for each collection).

The URL address contains collection name followed with a page number. For example, the 3rd page of the `blog` collection is under `https://example.com/blog/3`.

## Tags

Tags are available in valon. Yes, the feature has been ported from Astro Micro, but in valon

- tags are available for all collections, not only for the blog
- tags filtering page supports pagination, exactly like the collection pages

## Individual OpenGraph images

Each post can have its own OpenGraph image defined by `ogImage` property in the post metadata section. If no image is provided, the global OpenGraph image (`/og-image.png`) is used.

## Last update date

For the posts with updated content, you can provide the `lastUpdateDate` in the document metadata. It's displayed next to the publish date on the post page and in the buttons.
If `lastUpdateDate` property is defined, it takes precedence over `date` in sorting.

## Graphical logo

valon allows to set up a graphical logo. The max dimensions are set to 240px width and 50px height. Your image can be larger, but it will be rendered to fit the dimensions.

Two versions are supported: light and dark. The logo is configured in the `GLOBAL` variable defined in `src/consts.ts` file.

If no logo is provided, valon fallbacks to `GLOBAL.title` text.

## License

All posts have a license information. The license is displayed below the post content and is configurable in the `src/consts.ts` file (single license for all of the posts).

## Bio

On the home site and below each post, you can find a bio section from `src/components/Bio.astro` file.

## Refactor

valon has the "dev" in the name not only because it's tailored for the software engineers' needs.

I'm a backend engineer and refactored a lot of original Astro Nano and Astro Micro code in the "backendish" way.

Sometimes it was a small change, sometimes a complete rewrite. The changes contain but are not limited to:

- Single function that handles collection fetching, filtering and sorting. Now you change the logic in `getFilteredCollectionEntries` and you see the effects on the collection's page, in the navigation order, on the home site, etc. All of the places use it as a source of truth.
- Upgraded Astro and all packages to the latest version.
- Using variables from `consts` consistently across the sites. The `consts.ts` file itself also have more intuitive structure.
- Unified and standardized the arrow card buttons.
- Unified date formatting.
- Removed grouping collection entires by year. Added dates to the card buttons instead.
- And a lot of other small fixes, refactorings and cleaning up unused codes.

It's more maintainable and easier to extend now, so if you want to add new features or change the existing ones, you should find it easier to do in valon than in the original Astro Micro / Astro Nano.

## Original Astro Micro changelog

valon is based on [Astro Micro](https://astro-micro.vercel.app/) theme ([commit a234f5e](https://github.com/trevortylerlee/astro-micro/tree/a234f5e520b258f5ede923f8b8a4cbba03619158) to be precise).

Below is the original changelog of Astro Micro describing differences between Astro Nano that remained in valon.

See [Everything new in the valon](../everything-new-in-valon) to see what's new in valon.

### Pagefind search 🔎

[Pagefind](https://pagefind.app) is a search library for static websites. Micro uses [Sergey Shishkin's](https://github.com/shishkin) [astro-pagefind](https://github.com/shishkin/astro-pagefind) integration. This integration simplifies development and does not require any changes to the default build script.

Press <kbd>/</kbd> or <kbd>CTRL</kbd> + <kbd>K</kbd> to open the search dialog. For Mac users, <kbd>⌘</kbd> + <kbd>K</kbd> can also be used. To dismiss the search dialog, press <kbd>Esc</kbd> or click on an area outside the component.

### Build and develop

<Callout type="error">
  The site **must** be built at least once so Pagefind can index the content.
</Callout>

```bash
# Pagefind must index the site to function
npm run build
```

When developing you can continue to use `npm run dev` and Pagefind will use the index from the last available build.

---

### Giscus comments 💬

[Giscus](https://giscus.app) leverages Github discussions to act as a comments system. To get Giscus working on your own website, see [here](../getting-started#set-up-gisqus).

---

### Callout component 🆕

<Callout>
  Adipisicing et officia reprehenderit fugiat occaecat cupidatat exercitation
  labore consequat ullamco nostrud non.
</Callout>

<Callout type="info">
  Adipisicing et officia reprehenderit fugiat occaecat cupidatat exercitation
  labore consequat ullamco nostrud non.
</Callout>

<Callout type="warning">
  Adipisicing et officia reprehenderit fugiat occaecat cupidatat exercitation
  labore consequat ullamco nostrud non.
</Callout>

<Callout type="error">
  Adipisicing et officia reprehenderit fugiat occaecat cupidatat exercitation
  labore consequat ullamco nostrud non.
</Callout>

---

### UI enhancements 🎨

- Elements are styled and animate on focus
- Increased contrast in light mode
- Active theme is indicated by theme buttons
- Separate syntax highlight themes for light and dark mode
- Code blocks have a copy button
- Add pagination to the bottom of blog posts
- Create 404 page
- Add ToC component to posts

---

### Other changes

- Change fonts to Geist Sans and Geist Mono
- Switch base color from "stone" to "neutral"
- Slightly sped-up animations
- Reversed animation
- Ensure posts use an h1 tag for post titles
- Tweaked typography
