---
import Head from "@components/Head.astro";
import Header from "@components/Header.astro";
import Footer from "@components/Footer.astro";
import PageFind from "@components/PageFind.astro";
import { GLOBAL } from "@consts";

type Props = {
  title?: string;
  description?: string;
  ogImage?: string;
};

const { title, description, ogImage } = Astro.props;

const finalTitle = title ? `${title} - ${GLOBAL.title}` : GLOBAL.title;
---

<!doctype html>
<html lang="en">
  <head>
    <Head title={finalTitle} {description} {ogImage} />
  </head>
  <body>
    <Header />
    <main>
      <slot />
    </main>
    <Footer />
    <PageFind />
  </body>
</html>
