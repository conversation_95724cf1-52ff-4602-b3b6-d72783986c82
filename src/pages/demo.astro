---
import Layout from "@layouts/Layout.astro";
import Container from "@components/Container.astro";
import Throbbers from "@components/Throbbers.astro";
import CodeRunner from "@components/CodeRunner.astro";
import PipelineDemo from "@components/PipelineDemo.astro";
import ContactForm from "@components/ContactForm.astro";
import LazyImage from "@components/LazyImage.astro";
---

<Layout title="Interactive Demo" description="Showcase of interactive components with loading states">
  <Container>
    <div class="prose prose-neutral dark:prose-invert max-w-none">
      <h1>Interactive Components Demo</h1>
      <p>This page showcases various interactive components with loading states using our Throbbers component.</p>
      
      <h2>1. All Throbber Types</h2>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6 not-prose my-8">
        <div class="text-center p-4 border border-black/15 dark:border-white/20 rounded">
          <Throbbers type="dots" size="md" color="#89B4FA" />
          <p class="mt-2 text-sm">Dots</p>
        </div>
        <div class="text-center p-4 border border-black/15 dark:border-white/20 rounded">
          <Throbbers type="spin" size="md" color="#A6E3A1" />
          <p class="mt-2 text-sm">Spin</p>
        </div>
        <div class="text-center p-4 border border-black/15 dark:border-white/20 rounded">
          <Throbbers type="pulse" size="md" color="#F38BA8" />
          <p class="mt-2 text-sm">Pulse</p>
        </div>
        <div class="text-center p-4 border border-black/15 dark:border-white/20 rounded">
          <Throbbers type="bar" size="md" color="#FAB387" />
          <p class="mt-2 text-sm">Bar</p>
        </div>
        <div class="text-center p-4 border border-black/15 dark:border-white/20 rounded">
          <Throbbers type="bounce" size="md" color="#CBA6F7" />
          <p class="mt-2 text-sm">Bounce</p>
        </div>
        <div class="text-center p-4 border border-black/15 dark:border-white/20 rounded">
          <Throbbers type="grid" size="md" color="#F9E2AF" />
          <p class="mt-2 text-sm">Grid</p>
        </div>
        <div class="text-center p-4 border border-black/15 dark:border-white/20 rounded">
          <Throbbers type="ring" size="md" color="#94E2D5" />
          <p class="mt-2 text-sm">Ring</p>
        </div>
        <div class="text-center p-4 border border-black/15 dark:border-white/20 rounded">
          <Throbbers type="orbit" size="md" color="#89B4FA" />
          <p class="mt-2 text-sm">Orbit</p>
        </div>
      </div>
      
      <h2>2. Code Runner Component</h2>
      <p>Interactive code examples with loading states:</p>
      
      <CodeRunner 
        code={`# Data ingestion example
import pandas as pd
import requests

def fetch_weather_data(city="London"):
    # Simulate API call
    data = {
        "city": city,
        "temperature": 15.2,
        "humidity": 76
    }
    return pd.DataFrame([data])

df = fetch_weather_data()
print(df)`}
        language="python"
        title="Weather Data Fetcher"
        output={`     city  temperature  humidity
0  London         15.2        76`}
      />
      
      <h2>3. Data Pipeline Visualization</h2>
      <p>Watch a data pipeline execute step by step:</p>
      
      <PipelineDemo 
        title="Weather Data ETL Pipeline"
        steps={[
          { name: "Extract", description: "Fetching weather data from OpenWeatherMap API..." },
          { name: "Transform", description: "Converting temperature units and cleaning data..." },
          { name: "Load", description: "Storing processed data in SQLite database..." },
          { name: "Validate", description: "Running data quality checks..." }
        ]}
      />
      
      <h2>4. Contact Form</h2>
      <p>A contact form with loading states and feedback:</p>
      
      <div class="not-prose">
        <ContactForm />
      </div>
      
      <h2>5. Lazy Loading Images</h2>
      <p>Images with loading states (these are placeholder images):</p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 not-prose my-8">
        <LazyImage 
          src="https://picsum.photos/400/300?random=1" 
          alt="Random image 1"
          class="h-48"
        />
        <LazyImage 
          src="https://picsum.photos/400/300?random=2" 
          alt="Random image 2"
          class="h-48"
        />
      </div>
      
      <h2>6. Search Functionality</h2>
      <p>Try the search functionality (press <kbd>/</kbd> or <kbd>Ctrl+K</kbd>) to see the loading state when searching.</p>
      
      <h2>7. Page Transitions</h2>
      <p>Navigate between pages to see the page loading transitions in action.</p>
      
      <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
        <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Usage in Your Content</h3>
        <p class="text-blue-700 dark:text-blue-300 text-sm">
          You can use these components in your blog posts and tutorials to create more engaging, interactive content. 
          The throbbers provide visual feedback during loading states, improving the user experience.
        </p>
      </div>
    </div>
  </Container>
</Layout>
