---
import ArrowCardButton from "@components/ArrowCardButton.astro";
import ContactForm from "@components/ContactForm.astro";
import RootPageIndex from "@components/RootPageIndex.astro";

import { CONTACT } from "@consts";
import { resolvePath } from "@lib/utils";

const linkedInContact = CONTACT.find((contact) => contact.type === "LinkedIn");
---

<RootPageIndex title="Hire me" searchable>
  <div class="animate mb-4">
    <p class="mb-1">The PDF resume:</p>
    <section>
      <ArrowCardButton
        href={resolvePath("/hire-me/valon_resume.pdf", Astro.url.pathname)}
        text="Download PDF"
        openInNewTab
      />
    </section>
  </div>
  <div class="animate mb-4">
    <p class="mb-1">The TXT resume:</p>
    <section>
      <ArrowCardButton
        href={resolvePath("/hire-me/valon_resume.txt", Astro.url.pathname)}
        text="Download TXT"
        openInNewTab
      />
    </section>
  </div>

  {
    linkedInContact && (
      <div class="animate mb-4">
        <p class="mb-1">Or you can check my LinkedIn profile:</p>
        <section>
          <ArrowCardButton
            href={linkedInContact.href}
            text="Navigate to LinkedIn"
            openInNewTab
          />
        </section>
      </div>
    )
  }

  <div class="animate mt-8">
    <h2 class="mb-4 text-xl font-semibold text-black dark:text-white">
      Get in Touch
    </h2>
    <p class="mb-6 text-neutral-600 dark:text-neutral-400">
      Interested in working together? Send me a message and I'll get back to you
      as soon as possible.
    </p>
    <ContactForm />
  </div>
</RootPageIndex>
